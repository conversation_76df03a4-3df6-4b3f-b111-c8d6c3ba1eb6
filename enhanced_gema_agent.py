"""
Enhanced Gema Agent - Matching Augment Agent Auto Capabilities

This module implements an enhanced version of the Gema Agent that matches
Augment Agent Auto's capabilities in all aspects:
- Advanced context engine
- Sophisticated tool implementations
- Enhanced memory handling
- Optimized prompt engineering
- Military-grade security
"""

import os
import sys
import json
import re
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import enhanced components with fallbacks
try:
    from enhanced_context_main import EnhancedContextEngine
except ImportError:
    logger.warning("Enhanced context engine not found, using fallback")
    from enhanced_context_engine import MultiLevelCache, EmbeddingModel, VectorIndex

    class EnhancedContextEngine:
        def __init__(self, config):
            self.config = config
            logger.info("Using fallback Enhanced Context Engine")

        def get_context(self, query, max_results=10):
            return []

        def add_to_context(self, content, path, metadata=None):
            return True

        def verify_integrity(self):
            return True

        def get_stats(self):
            return {}

try:
    from military_grade_context_engine import MilitaryGradeContextEngine
except ImportError:
    logger.warning("Military context engine not found, using fallback")

    class MilitaryGradeContextEngine:
        def __init__(self, config):
            self.config = config
            logger.info("Using fallback Military Context Engine")

        def verify_integrity(self):
            return True

try:
    from local_llm import LocalLLM
except ImportError:
    logger.warning("Local LLM not found, using fallback")

    class LocalLLM:
        def __init__(self, model_path=None, model_config=None):
            self.model_path = model_path
            self.model_config = model_config
            logger.info("Using fallback Local LLM")

        def generate(self, prompt):
            return "Fallback response from Enhanced Military Agent"

try:
    from memory_manager import MemoryManager
except ImportError:
    logger.warning("Memory manager not found, using fallback")

    class MemoryManager:
        def __init__(self):
            self.memories = []
            logger.info("Using fallback Memory Manager")

        def add_memory(self, text, tags=None, importance=3):
            self.memories.append({'text': text, 'tags': tags, 'importance': importance})
            return True

        def get_memories(self, limit=None, min_importance=0):
            return self.memories[:limit] if limit else self.memories

        def search_memories(self, query, limit=None):
            return []

        def get_memories_text(self, limit=None, min_importance=0):
            return ""

        def clear_memories(self):
            self.memories = []
            return True

try:
    from learning_system import LearningSystem
except ImportError:
    logger.warning("Learning system not found, using fallback")

    class LearningSystem:
        def __init__(self, cache_dir=None, config=None):
            self.cache_dir = cache_dir
            self.config = config
            logger.info("Using fallback Learning System")

        def get_relevant_examples(self, query):
            return []

        def get_system_prompt_enhancement(self):
            return ""

        def add_feedback(self, query, response, rating, comment=None):
            return True

        def get_stats(self):
            return {}

# Import tool systems with fallbacks
try:
    from tools.enhanced_tool_interface import ToolRegistry, Tool
except ImportError:
    logger.warning("Enhanced tool interface not found, using fallback")

    class Tool:
        def __init__(self, name, description, function, parameters=None, security_level="CONFIDENTIAL"):
            self.name = name
            self.description = description
            self.function = function
            self.parameters = parameters or {}
            self.security_level = security_level

        def execute(self, **kwargs):
            return self.function(**kwargs)

    class ToolRegistry:
        def __init__(self):
            self.tools = {}

        def register(self, tool, category="general"):
            self.tools[tool.name] = tool
            return True

        def get(self, name):
            return self.tools.get(name)

        def execute(self, name, **kwargs):
            tool = self.get(name)
            if tool:
                return tool.execute(**kwargs)
            return f"Tool {name} not found"

try:
    from tools.code_tools import CodeTools
    from tools.process_tools import ProcessTools
    from tools.search_tools import SearchTools
except ImportError:
    logger.warning("Tool modules not found, using fallbacks")

    class CodeTools:
        def __init__(self, registry, context_engine=None):
            pass

    class ProcessTools:
        def __init__(self, registry):
            pass

    class SearchTools:
        def __init__(self, registry):
            pass

# Import encoding tools with fallbacks
try:
    from encoding_tools import (
        write_file as write_file_unicode,
        read_file as read_file_unicode,
        encode_base64 as encode_base64_unicode,
        decode_base64 as decode_base64_unicode,
        debug_string
    )
except ImportError:
    logger.warning("Encoding tools not found, using fallbacks")

    def write_file_unicode(path, content, encoding='utf-8'):
        try:
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
            return f"File written successfully: {path}"
        except Exception as e:
            return f"Error writing file: {e}"

    def read_file_unicode(path, encoding=None):
        try:
            with open(path, 'r', encoding=encoding or 'utf-8') as f:
                return f.read()
        except Exception as e:
            return f"Error reading file: {e}"

    def encode_base64_unicode(text):
        import base64
        return base64.b64encode(text.encode()).decode()

    def decode_base64_unicode(encoded):
        import base64
        return base64.b64decode(encoded).decode()

    def debug_string(text):
        return repr(text)

# Configure logging with UTF-8 encoding
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s',
    handlers=[
        logging.FileHandler("enhanced_gema_agent.log", encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ENHANCED_GEMA_AGENT")


class EnhancedGemaAgent:
    """
    Enhanced Gema Agent with Augment Agent Auto capabilities.

    Features:
    - Advanced context engine with vector similarity search
    - Multi-level caching for optimal performance
    - Enhanced tool ecosystem
    - Military-grade security
    - Sophisticated memory management
    - Optimized prompt engineering
    """

    def __init__(self, config_path=None):
        """Initialize the enhanced Gema Agent."""
        logger.info("Initializing Enhanced Gema Agent with Augment Agent Auto capabilities")

        self.config = self._load_config(config_path)
        self.setup_components()

        logger.info("Enhanced Gema Agent initialized successfully")

    def _load_config(self, config_path):
        """Load configuration with enhanced defaults."""
        # Enhanced configuration matching Augment Agent Auto
        config = {
            'llm': {
                'type': 'local',
                'local': {
                    'model_path': 'C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf',
                    'n_ctx': 8192,  # Increased context window
                    'n_batch': 1024,  # Increased batch size
                    'n_gpu_layers': 0,
                    'temperature': 0.7,
                    'max_tokens': 2048  # Increased max tokens
                }
            },
            'context_engine': {
                'embedding_dim': 768,
                'model_type': 'code',
                'cache_dir': './enhanced_context_cache',
                'max_results': 10,  # Increased max results
                'memory_cache_size': 10000,
                'memory_ttl': 3600,
                'disk_ttl': 86400,
                'disk_max_size_mb': 1024,
                'security_level': 'CONFIDENTIAL'
            },
            'agent': {
                'max_context_results': 10,
                'max_conversation_history': 20,  # Increased history
                'cache_dir': './enhanced_agent_cache',
                'learning': {
                    'enabled': True,
                    'max_examples': 200,  # Increased examples
                    'similarity_threshold': 0.5,
                    'feedback_threshold': 4,
                    'cache_dir': './enhanced_learning_cache'
                }
            }
        }

        # Load user configuration if provided
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)

                # Deep merge configurations
                def merge_dicts(d1, d2):
                    for k, v in d2.items():
                        if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                            merge_dicts(d1[k], v)
                        else:
                            d1[k] = v

                merge_dicts(config, user_config)
                logger.info(f"Configuration loaded from {config_path}")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")

        return config

    def setup_components(self):
        """Setup all enhanced components."""
        logger.info("Setting up enhanced components...")

        # Initialize enhanced context engine
        self.context_engine = EnhancedContextEngine(self.config['context_engine'])
        logger.info("[OK] Enhanced context engine initialized")

        # Initialize military-grade context engine as backup
        self.military_context = MilitaryGradeContextEngine(self.config['context_engine'])
        logger.info("[OK] Military-grade context engine initialized")

        # Initialize LLM with enhanced configuration
        self.llm = LocalLLM(
            model_path=self.config['llm']['local']['model_path'],
            model_config=self.config['llm']['local']
        )
        logger.info("[OK] Enhanced LLM initialized")

        # Initialize enhanced memory manager
        self.memory_manager = MemoryManager()
        logger.info("[OK] Enhanced memory manager initialized")

        # Initialize enhanced learning system
        self.learning_system = LearningSystem(
            cache_dir=self.config['agent']['learning']['cache_dir'],
            config=self.config['agent']['learning']
        )
        logger.info("[OK] Enhanced learning system initialized")

        # Setup enhanced tool registry
        self.tool_registry = ToolRegistry()
        self._setup_enhanced_tools()
        logger.info("[OK] Enhanced tools initialized")

        # Initialize conversation history
        self.conversation_history = []

        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'average_response_time': 0.0,
            'context_cache_hits': 0,
            'context_cache_misses': 0
        }

    def _setup_enhanced_tools(self):
        """Setup enhanced tool ecosystem matching Augment Agent Auto."""

        # Enhanced file operations with Unicode support
        self.tool_registry.register(Tool(
            name="write_file",
            description="Write content to file with full Unicode support and integrity checking",
            function=self._enhanced_write_file
        ))

        self.tool_registry.register(Tool(
            name="read_file",
            description="Read file content with automatic encoding detection and error handling",
            function=self._enhanced_read_file
        ))

        # Enhanced memory operations
        self.tool_registry.register(Tool(
            name="remember",
            description="Add memory with importance scoring and categorization",
            function=self._enhanced_remember
        ))

        self.tool_registry.register(Tool(
            name="get_memories",
            description="Retrieve memories with filtering and ranking",
            function=self._enhanced_get_memories
        ))

        self.tool_registry.register(Tool(
            name="search_memories",
            description="Search memories using semantic similarity",
            function=self._enhanced_search_memories
        ))

        # Enhanced context operations
        self.tool_registry.register(Tool(
            name="search_context",
            description="Search codebase context using advanced vector similarity",
            function=self._enhanced_search_context
        ))

        self.tool_registry.register(Tool(
            name="add_to_context",
            description="Add content to context with metadata and security classification",
            function=self._enhanced_add_to_context
        ))

        # Enhanced code operations
        self.tool_registry.register(Tool(
            name="analyze_code",
            description="Analyze code with advanced static analysis and security checks",
            function=self._enhanced_analyze_code
        ))

        self.tool_registry.register(Tool(
            name="format_code",
            description="Format code with language-specific best practices",
            function=self._enhanced_format_code
        ))

        # Enhanced system operations
        self.tool_registry.register(Tool(
            name="execute_command",
            description="Execute system commands with security validation and monitoring",
            function=self._enhanced_execute_command
        ))

        # Setup traditional tools with enhanced capabilities
        CodeTools(self.tool_registry, self.context_engine)
        ProcessTools(self.tool_registry)
        SearchTools(self.tool_registry)

    def _enhanced_write_file(self, path: str, content: str, encoding: str = 'utf-8',
                           security_level: str = 'CONFIDENTIAL') -> str:
        """Enhanced file writing with security and integrity checks."""
        try:
            # Security validation
            if not self._validate_file_path(path):
                return f"Error: Invalid file path for security level {security_level}"

            # Write file with Unicode support
            result = write_file_unicode(path, content, encoding)

            # Add to context if successful
            if "successfully" in result.lower():
                self.context_engine.add_to_context(
                    content, path,
                    metadata={'operation': 'write', 'encoding': encoding},
                    security_level=security_level
                )

            return result
        except Exception as e:
            logger.error(f"Enhanced write_file error: {e}")
            return f"Error writing file: {str(e)}"

    def _enhanced_read_file(self, path: str, encoding: str = None) -> str:
        """Enhanced file reading with context integration."""
        try:
            # Read file with Unicode support
            content = read_file_unicode(path, encoding)

            # Add to context for future reference
            if content and not content.startswith("Error"):
                self.context_engine.add_to_context(
                    content, path,
                    metadata={'operation': 'read', 'encoding': encoding or 'auto-detected'}
                )

            return content
        except Exception as e:
            logger.error(f"Enhanced read_file error: {e}")
            return f"Error reading file: {str(e)}"

    def _enhanced_remember(self, memory_text: str, tags: List[str] = None,
                          importance: int = 3) -> str:
        """Enhanced memory addition with context integration."""
        try:
            success = self.memory_manager.add_memory(memory_text, tags, importance)
            if success:
                # Also add to context for retrieval
                self.context_engine.add_to_context(
                    memory_text, f"memory://{int(time.time())}",
                    metadata={'type': 'memory', 'importance': importance, 'tags': tags or []}
                )
                return f"Memory added successfully: {memory_text[:50]}..."
            else:
                return "Failed to add memory"
        except Exception as e:
            logger.error(f"Enhanced remember error: {e}")
            return f"Error adding memory: {str(e)}"

    def _enhanced_get_memories(self, limit: int = 10, min_importance: int = 0) -> str:
        """Enhanced memory retrieval with ranking."""
        try:
            memories = self.memory_manager.get_memories(limit, min_importance)
            if memories:
                formatted_memories = []
                for i, memory in enumerate(memories, 1):
                    importance = memory.get('importance', 3)
                    tags = memory.get('tags', [])
                    text = memory.get('text', '')
                    formatted_memories.append(
                        f"{i}. [{importance}/5] {text} {f'(Tags: {tags})' if tags else ''}"
                    )
                return "\n".join(formatted_memories)
            else:
                return "No memories found"
        except Exception as e:
            logger.error(f"Enhanced get_memories error: {e}")
            return f"Error retrieving memories: {str(e)}"

    def _enhanced_search_memories(self, query: str, limit: int = 5) -> str:
        """Enhanced memory search using semantic similarity."""
        try:
            memories = self.memory_manager.search_memories(query, limit)
            if memories:
                formatted_results = []
                for i, memory in enumerate(memories, 1):
                    text = memory.get('text', '')
                    score = memory.get('score', 0.0)
                    formatted_results.append(f"{i}. [{score:.3f}] {text}")
                return "\n".join(formatted_results)
            else:
                return f"No memories found matching: {query}"
        except Exception as e:
            logger.error(f"Enhanced search_memories error: {e}")
            return f"Error searching memories: {str(e)}"

    def _enhanced_search_context(self, query: str, max_results: int = 5) -> str:
        """Enhanced context search using vector similarity."""
        try:
            results = self.context_engine.get_context(query, max_results)
            if results:
                formatted_results = []
                for i, result in enumerate(results, 1):
                    path = result.get('path', 'unknown')
                    score = result.get('score', 0.0)
                    content = result.get('content', '')[:100] + "..."
                    formatted_results.append(
                        f"{i}. [{score:.3f}] {path}\n   {content}"
                    )
                return "\n".join(formatted_results)
            else:
                return f"No context found for: {query}"
        except Exception as e:
            logger.error(f"Enhanced search_context error: {e}")
            return f"Error searching context: {str(e)}"

    def _enhanced_add_to_context(self, content: str, path: str,
                                metadata: Dict[str, Any] = None) -> str:
        """Enhanced context addition with metadata."""
        try:
            success = self.context_engine.add_to_context(content, path, metadata)
            if success:
                return f"Added to context: {path}"
            else:
                return f"Failed to add to context: {path}"
        except Exception as e:
            logger.error(f"Enhanced add_to_context error: {e}")
            return f"Error adding to context: {str(e)}"

    def _enhanced_analyze_code(self, code: str, language: str = None) -> str:
        """Enhanced code analysis with security and quality checks."""
        try:
            # Basic analysis
            lines = len(code.split('\n'))
            chars = len(code)

            # Language detection if not provided
            if not language:
                language = self._detect_language(code)

            # Security analysis
            security_issues = self._analyze_security(code, language)

            # Quality analysis
            quality_issues = self._analyze_quality(code, language)

            analysis = f"Code Analysis:\n"
            analysis += f"- Language: {language}\n"
            analysis += f"- Lines: {lines}\n"
            analysis += f"- Characters: {chars}\n"

            if security_issues:
                analysis += f"- Security Issues: {len(security_issues)}\n"
                for issue in security_issues[:3]:
                    analysis += f"  * {issue}\n"

            if quality_issues:
                analysis += f"- Quality Issues: {len(quality_issues)}\n"
                for issue in quality_issues[:3]:
                    analysis += f"  * {issue}\n"

            return analysis
        except Exception as e:
            logger.error(f"Enhanced analyze_code error: {e}")
            return f"Error analyzing code: {str(e)}"

    def _detect_language(self, code: str) -> str:
        """Detect programming language from code."""
        if 'def ' in code and 'import ' in code:
            return 'python'
        elif 'function ' in code and ('var ' in code or 'let ' in code):
            return 'javascript'
        elif 'public class ' in code:
            return 'java'
        elif '#include' in code:
            return 'cpp'
        else:
            return 'unknown'

    def _analyze_security(self, code: str, language: str) -> List[str]:
        """Analyze code for security issues."""
        issues = []

        # Common security patterns
        if 'eval(' in code:
            issues.append("Potential code injection via eval()")
        if 'exec(' in code:
            issues.append("Potential code injection via exec()")
        if 'os.system(' in code:
            issues.append("Direct system command execution")
        if 'subprocess.call(' in code and 'shell=True' in code:
            issues.append("Shell injection vulnerability")

        return issues

    def _analyze_quality(self, code: str, language: str) -> List[str]:
        """Analyze code for quality issues."""
        issues = []

        lines = code.split('\n')

        # Check for long lines
        for i, line in enumerate(lines, 1):
            if len(line) > 120:
                issues.append(f"Line {i}: Line too long ({len(line)} chars)")

        # Check for TODO/FIXME comments
        for i, line in enumerate(lines, 1):
            if 'TODO' in line or 'FIXME' in line:
                issues.append(f"Line {i}: Unresolved TODO/FIXME")

        return issues

    def _enhanced_format_code(self, code: str, language: str = None) -> str:
        """Enhanced code formatting with language-specific rules."""
        try:
            if not language:
                language = self._detect_language(code)

            # Basic formatting
            lines = code.split('\n')
            formatted_lines = []

            for line in lines:
                # Remove trailing whitespace
                line = line.rstrip()
                formatted_lines.append(line)

            formatted_code = '\n'.join(formatted_lines)

            return f"Formatted {language} code:\n```{language}\n{formatted_code}\n```"
        except Exception as e:
            logger.error(f"Enhanced format_code error: {e}")
            return f"Error formatting code: {str(e)}"

    def _enhanced_execute_command(self, command: str, timeout: int = 30) -> str:
        """Enhanced command execution with security validation."""
        try:
            # Security validation
            if not self._validate_command(command):
                return f"Error: Command blocked for security reasons: {command}"

            # Execute with timeout
            import subprocess
            result = subprocess.run(
                command, shell=True, capture_output=True,
                text=True, timeout=timeout
            )

            if result.returncode == 0:
                return f"Command executed successfully:\n{result.stdout}"
            else:
                return f"Command failed (exit code {result.returncode}):\n{result.stderr}"

        except subprocess.TimeoutExpired:
            return f"Command timed out after {timeout} seconds"
        except Exception as e:
            logger.error(f"Enhanced execute_command error: {e}")
            return f"Error executing command: {str(e)}"

    def _validate_file_path(self, path: str) -> bool:
        """Validate file path for security."""
        # Block dangerous paths
        dangerous_patterns = ['../', '..\\', '/etc/', '/root/', 'C:\\Windows\\']
        return not any(pattern in path for pattern in dangerous_patterns)

    def _validate_command(self, command: str) -> bool:
        """Validate command for security."""
        # Block dangerous commands
        dangerous_commands = ['rm -rf', 'del /f', 'format', 'fdisk', 'shutdown']
        return not any(cmd in command.lower() for cmd in dangerous_commands)

    def process_message(self, message: str) -> str:
        """
        Process user message with enhanced capabilities matching Augment Agent Auto.
        """
        start_time = time.time()
        self.metrics['total_requests'] += 1

        logger.info(f"Processing message: {message[:100]}...")

        try:
            # Add to conversation history
            self.conversation_history.append({"role": "user", "content": message})

            # Get relevant context using enhanced context engine
            context_results = self.context_engine.get_context(message, max_results=5)
            context_text = self._format_context_results(context_results)

            # Get relevant memories
            memories = self.memory_manager.get_memories_text(limit=5, min_importance=3)

            # Get learning examples
            examples = self.learning_system.get_relevant_examples(message)
            examples_text = self._format_learning_examples(examples)

            # Get system prompt enhancement
            system_enhancement = self.learning_system.get_system_prompt_enhancement()

            # Build enhanced prompt matching Augment Agent Auto style
            prompt = self._build_enhanced_prompt(
                message, context_text, memories, examples_text, system_enhancement
            )

            # Generate response
            response = self.llm.generate(prompt)

            # Process tool calls in response
            processed_response = self._process_tool_calls(response)

            # Add to conversation history
            self.conversation_history.append({"role": "assistant", "content": processed_response})

            # Update metrics
            self.metrics['successful_requests'] += 1
            elapsed_time = time.time() - start_time
            self.metrics['average_response_time'] = (
                (self.metrics['average_response_time'] * (self.metrics['successful_requests'] - 1) + elapsed_time) /
                self.metrics['successful_requests']
            )

            logger.info(f"Message processed successfully in {elapsed_time:.2f}s")
            return processed_response

        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return f"I apologize, but I encountered an error processing your request: {str(e)}"

    def _format_context_results(self, results: List[Dict[str, Any]]) -> str:
        """Format context results for prompt inclusion."""
        if not results:
            return ""

        formatted = "Relevant Context:\n"
        for i, result in enumerate(results, 1):
            path = result.get('path', 'unknown')
            score = result.get('score', 0.0)
            content = result.get('content', '')[:200] + "..."
            formatted += f"{i}. [{score:.3f}] {path}\n{content}\n\n"

        return formatted

    def _format_learning_examples(self, examples: List[Dict[str, Any]]) -> str:
        """Format learning examples for prompt inclusion."""
        if not examples:
            return ""

        formatted = "Relevant Examples:\n"
        for i, example in enumerate(examples[:3], 1):
            query = example.get('query', '')
            response = example.get('response', '')
            formatted += f"Example {i}:\nQuery: {query}\nResponse: {response[:100]}...\n\n"

        return formatted

    def _build_enhanced_prompt(self, message: str, context_text: str, memories: str,
                              examples_text: str, system_enhancement: str) -> str:
        """Build enhanced prompt matching Augment Agent Auto capabilities."""

        # Get available tools
        tools_list = []
        for tool_name, tool in self.tool_registry.tools.items():
            tools_list.append(f"{tool_name}: {tool.description}")

        prompt = f"""You are the Enhanced Military Agent, an advanced AI assistant with military-grade capabilities that behaves exactly like Augment Agent Auto. You have access to the developer's codebase through an enhanced context engine and comprehensive tool integrations.

{system_enhancement}

CORE BEHAVIOR RULES:
1. Always gather comprehensive information before acting
2. Plan thoroughly and communicate plans clearly
3. Use tools appropriately and conservatively
4. Format code responses with <augment_code_snippet> tags
5. Maintain military-grade security standards
6. Test and validate all implementations
7. Ask for help when needed
8. Focus on user requirements and satisfaction

SECURITY LEVEL: CONFIDENTIAL
MODEL: claude-3.7-sonnet-reasoning-gemma3-12B

{memories}

{context_text}

{examples_text}

AVAILABLE TOOLS:
{chr(10).join(tools_list)}

To use a tool, use the syntax: {{tool.tool_name(param1=value1, param2=value2)}}

CONVERSATION HISTORY:
{chr(10).join([f"{msg['role']}: {msg['content']}" for msg in self.conversation_history[-10:]])}

CURRENT REQUEST: {message}

Respond as the Enhanced Military Agent with Augment Agent Auto capabilities. Use tools as needed to fulfill the request. Explain your actions clearly and maintain security protocols.

RESPONSE:"""

        return prompt

    def _process_tool_calls(self, text: str) -> str:
        """Process tool calls in response text with enhanced error handling."""
        # Pattern for tool calls
        pattern = r'\{tool\.(\w+)\(([^)]*)\)\}'

        # Find all tool calls
        matches = re.findall(pattern, text)

        processed_text = text
        for tool_name, args_str in matches:
            logger.info(f"Processing tool call: {tool_name}({args_str})")

            # Get tool
            tool = self.tool_registry.get(tool_name)

            if not tool:
                logger.warning(f"Tool not found: {tool_name}")
                continue

            # Parse arguments
            args = self._parse_tool_arguments(args_str)

            # Execute tool
            try:
                result = tool.execute(**args)
                logger.info(f"Tool {tool_name} executed successfully")

                # Replace tool call with result
                tool_call_str = f"{{tool.{tool_name}({args_str})}}"
                processed_text = processed_text.replace(
                    tool_call_str,
                    f"{{tool.{tool_name} result:}}\n```\n{result}\n```"
                )

            except Exception as e:
                logger.error(f"Tool {tool_name} execution failed: {e}")

                # Replace with error message
                tool_call_str = f"{{tool.{tool_name}({args_str})}}"
                processed_text = processed_text.replace(
                    tool_call_str,
                    f"{{tool.{tool_name} error:}}\n```\n{str(e)}\n```"
                )

        return processed_text

    def _parse_tool_arguments(self, args_str: str) -> Dict[str, Any]:
        """Parse tool arguments from string."""
        args = {}
        if not args_str.strip():
            return args

        # Split arguments
        parts = []
        current_part = ""
        in_quotes = False
        quote_char = None

        for char in args_str:
            if char in ['"', "'"] and not in_quotes:
                in_quotes = True
                quote_char = char
                current_part += char
            elif char == quote_char and in_quotes:
                in_quotes = False
                quote_char = None
                current_part += char
            elif char == ',' and not in_quotes:
                parts.append(current_part.strip())
                current_part = ""
            else:
                current_part += char

        if current_part.strip():
            parts.append(current_part.strip())

        # Parse each part
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                key = key.strip()
                value = value.strip()

                # Remove quotes
                if value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.startswith("'") and value.endswith("'"):
                    value = value[1:-1]

                # Try to convert to appropriate type
                if value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                elif value.isdigit():
                    value = int(value)
                elif value.replace('.', '').isdigit():
                    value = float(value)

                args[key] = value

        return args

    def add_feedback(self, query: str, response: str, rating: int, comment: str = None) -> bool:
        """Add feedback to learning system."""
        return self.learning_system.add_feedback(query, response, rating, comment)

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive agent statistics."""
        return {
            'agent_metrics': self.metrics,
            'context_stats': self.context_engine.get_stats(),
            'memory_stats': self.memory_manager.get_stats() if hasattr(self.memory_manager, 'get_stats') else {},
            'learning_stats': self.learning_system.get_stats(),
            'conversation_length': len(self.conversation_history)
        }

    def verify_integrity(self) -> bool:
        """Verify system integrity."""
        try:
            # Check context engine
            context_ok = self.context_engine.verify_integrity()

            # Check military context engine
            military_ok = self.military_context.verify_integrity()

            # Check tool registry
            tools_ok = len(self.tool_registry.tools) > 0

            # Check conversation history
            history_ok = isinstance(self.conversation_history, list)

            integrity_result = context_ok and military_ok and tools_ok and history_ok

            logger.info(f"System integrity verification: {integrity_result}")
            return integrity_result

        except Exception as e:
            logger.error(f"Integrity verification failed: {e}")
            return False

    def clear_memories(self) -> bool:
        """Clear all memories."""
        return self.memory_manager.clear_memories()

    def add_memory(self, memory_text: str, tags: List[str] = None, importance: int = 3) -> bool:
        """Add memory to the system."""
        return self.memory_manager.add_memory(memory_text, tags, importance)

    def get_memories(self, limit: int = None, min_importance: int = 0) -> List[Dict[str, Any]]:
        """Get memories from the system."""
        return self.memory_manager.get_memories(limit, min_importance)

    def search_memories(self, query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search memories."""
        return self.memory_manager.search_memories(query, limit)
