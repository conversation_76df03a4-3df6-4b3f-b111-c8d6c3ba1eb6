"""
Enhanced Context Engine Main Class - Matching Augment Agent Auto Capabilities

This module contains the main EnhancedContextEngine class that integrates all components
to provide Augment Agent Auto level performance and capabilities.
"""

import os
import json
import time
import hashlib
import logging
import concurrent.futures
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from enhanced_context_engine import MultiLevelCache, EmbeddingModel, VectorIndex

logger = logging.getLogger("ENHANCED_CONTEXT_MAIN")


class EnhancedContextEngine:
    """
    Enhanced Context Engine matching Augment Agent Auto's capabilities.

    Features:
    - Advanced embedding models
    - Multi-level caching
    - Vector similarity search
    - Performance optimizations
    - Military-grade security
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize enhanced context engine."""
        self.config = config or {}
        logger.info("Initializing Enhanced Context Engine with Augment Agent Auto capabilities")

        # Configuration
        self.embedding_dim = self.config.get('embedding_dim', 768)
        self.model_type = self.config.get('model_type', 'code')
        self.language = self.config.get('language', None)
        self.cache_dir = Path(self.config.get('cache_dir', './enhanced_context_cache'))
        self.max_results = self.config.get('max_results', 10)
        self.security_level = self.config.get('security_level', 'CONFIDENTIAL')

        # Create directories
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.data_dir = self.cache_dir / 'data'
        self.index_dir = self.cache_dir / 'index'
        self.data_dir.mkdir(exist_ok=True)
        self.index_dir.mkdir(exist_ok=True)

        # Initialize multi-level cache
        self.cache = MultiLevelCache(
            memory_cache_size=self.config.get('memory_cache_size', 10000),
            memory_ttl=self.config.get('memory_ttl', 3600),
            disk_cache_dir=str(self.cache_dir / 'cache'),
            disk_ttl=self.config.get('disk_ttl', 86400),
            disk_max_size_mb=self.config.get('disk_max_size_mb', 1024)
        )

        # Initialize embedding model
        self.embedding_model = EmbeddingModel(
            embedding_dim=self.embedding_dim,
            model_type=self.model_type,
            language=self.language,
            cache=self.cache
        )

        # Initialize vector index
        self.vector_index = VectorIndex(embedding_dim=self.embedding_dim)

        # Initialize thread pool
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config.get('max_threads', os.cpu_count() or 4)
        )

        # Context storage
        self.context_data = {}
        self.load_existing_context()

        # Add default context if empty
        if len(self.context_data) == 0:
            self._add_default_context()

        logger.info(f"Enhanced Context Engine initialized successfully with {len(self.context_data)} items")

    def get_context(self, query: str, max_results: int = None,
                   filter_criteria: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Get relevant context for a query using advanced retrieval.
        Matches Augment Agent Auto's context retrieval capabilities.
        """
        start_time = time.time()
        max_results = max_results or self.max_results

        # Check cache first
        cache_key = f"context_{hashlib.md5(query.encode()).hexdigest()}_{max_results}"
        cached_results = self.cache.get(cache_key)
        if cached_results is not None:
            logger.info(f"Cache hit for query: {query[:50]}...")
            return cached_results

        # Generate query embedding
        query_embedding = self.embedding_model.embed_query(query)

        # Search vector index
        search_results = self.vector_index.search(
            query_embedding,
            top_k=max(max_results * 2, 20),  # Get more for reranking
            filter_criteria=filter_criteria
        )

        # Rerank and enhance results
        enhanced_results = self._enhance_search_results(search_results, query)

        # Limit to requested number
        final_results = enhanced_results[:max_results]

        # Cache results
        self.cache.set(cache_key, final_results)

        # Log performance
        elapsed_time = time.time() - start_time
        logger.info(f"Context retrieval completed in {elapsed_time:.4f}s. {len(final_results)} results found.")

        return final_results

    def add_to_context(self, content: str, path: str, metadata: Dict[str, Any] = None,
                      security_level: str = None) -> bool:
        """
        Add content to context with advanced indexing.
        Matches Augment Agent Auto's context addition capabilities.
        """
        try:
            security_level = security_level or self.security_level
            metadata = metadata or {}

            # Generate unique ID
            doc_id = hashlib.md5(f"{path}:{content[:100]}".encode()).hexdigest()

            # Add metadata
            metadata.update({
                'path': path,
                'doc_id': doc_id,
                'added_at': datetime.now().isoformat(),
                'security_level': security_level,
                'content_hash': hashlib.sha256(content.encode()).hexdigest(),
                'content_length': len(content)
            })

            # Generate embedding
            embedding = self.embedding_model.embed_code(content)

            # Store in vector index
            self.vector_index.add(embedding, metadata)

            # Store content
            self.context_data[doc_id] = {
                'content': content,
                'metadata': metadata
            }

            # Save to disk
            self._save_context_item(doc_id, content, metadata)

            logger.info(f"Added to context: {path} (ID: {doc_id})")
            return True

        except Exception as e:
            logger.error(f"Error adding to context: {e}")
            return False

    def _enhance_search_results(self, search_results: List[Dict[str, Any]],
                               query: str) -> List[Dict[str, Any]]:
        """Enhance search results with additional context and reranking."""
        enhanced_results = []

        for result in search_results:
            doc_id = result.get('doc_id')
            if doc_id and doc_id in self.context_data:
                # Get full content
                content_data = self.context_data[doc_id]

                # Create enhanced result
                enhanced_result = {
                    'content': content_data['content'],
                    'path': result.get('path', ''),
                    'score': result.get('score', 0.0),
                    'metadata': content_data['metadata'],
                    'relevance_explanation': self._generate_relevance_explanation(
                        content_data['content'], query, result.get('score', 0.0)
                    )
                }

                enhanced_results.append(enhanced_result)

        return enhanced_results

    def _generate_relevance_explanation(self, content: str, query: str, score: float) -> str:
        """Generate explanation for why content is relevant."""
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())
        common_words = query_words.intersection(content_words)

        if common_words:
            return f"Matches keywords: {', '.join(list(common_words)[:3])} (similarity: {score:.3f})"
        else:
            return f"Semantic similarity: {score:.3f}"

    def _save_context_item(self, doc_id: str, content: str, metadata: Dict[str, Any]) -> None:
        """Save context item to disk."""
        try:
            item_path = self.data_dir / f"{doc_id}.json"
            with open(item_path, 'w', encoding='utf-8') as f:
                json.dump({
                    'content': content,
                    'metadata': metadata
                }, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save context item: {e}")

    def load_existing_context(self) -> None:
        """Load existing context from disk."""
        try:
            for item_path in self.data_dir.glob("*.json"):
                with open(item_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                doc_id = item_path.stem
                content = data['content']
                metadata = data['metadata']

                # Regenerate embedding and add to index
                embedding = self.embedding_model.embed_code(content)
                self.vector_index.add(embedding, metadata)

                # Store in memory
                self.context_data[doc_id] = data

            logger.info(f"Loaded {len(self.context_data)} context items from disk")

        except Exception as e:
            logger.warning(f"Error loading existing context: {e}")

    def clear_context(self, security_level: str = None) -> bool:
        """Clear context with security level filtering."""
        try:
            security_level = security_level or self.security_level

            # Clear in-memory data
            self.context_data.clear()
            self.vector_index = VectorIndex(embedding_dim=self.embedding_dim)

            # Clear disk cache
            for item_path in self.data_dir.glob("*.json"):
                item_path.unlink()

            logger.info("Context cleared successfully")
            return True

        except Exception as e:
            logger.error(f"Error clearing context: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics."""
        return {
            'context_items': len(self.context_data),
            'cache_stats': self.cache.stats,
            'embedding_stats': self.embedding_model.stats,
            'security_level': self.security_level,
            'model_type': self.model_type
        }

    def verify_integrity(self) -> bool:
        """Verify system integrity matching military standards."""
        try:
            # Check cache integrity
            cache_ok = hasattr(self.cache, 'stats') and isinstance(self.cache.stats, dict)

            # Check embedding model integrity
            embedding_ok = hasattr(self.embedding_model, 'stats') and isinstance(self.embedding_model.stats, dict)

            # Check vector index integrity
            index_ok = len(self.vector_index.vectors) == len(self.vector_index.metadata)

            # Check data consistency
            data_ok = len(self.context_data) <= len(self.vector_index.vectors)

            integrity_result = cache_ok and embedding_ok and index_ok and data_ok

            logger.info(f"Integrity verification: {integrity_result}")
            return integrity_result

        except Exception as e:
            logger.error(f"Integrity verification failed: {e}")
            return False

    def _add_default_context(self) -> None:
        """Add default context to help with initial queries."""
        try:
            default_contexts = [
                {
                    'content': 'Enhanced Military Agent is an advanced AI assistant with military-grade capabilities that behaves exactly like Augment Agent Auto. It has access to comprehensive tool integrations including file operations, web search, code analysis, process execution, and memory management.',
                    'path': 'system://agent_description',
                    'metadata': {'type': 'system', 'category': 'agent_info'}
                },
                {
                    'content': 'Available tools include: write_file, read_file, remember, get_memories, search_memories, search_context, add_to_context, analyze_code, format_code, execute_command, web_search, fetch_webpage, analyze_python_code, extract_functions, extract_classes, and many more.',
                    'path': 'system://available_tools',
                    'metadata': {'type': 'system', 'category': 'tools'}
                },
                {
                    'content': 'The agent uses a local LLM model at C:\\Users\\<USER>\\.lmstudio\\models\\reedmayhew\\claude-3.7-sonnet-reasoning-gemma3-12B\\claude-3.7-sonnet-reasoning-gemma3-12B.Q8_0.gguf with maximum context window of 131072 tokens.',
                    'path': 'system://llm_config',
                    'metadata': {'type': 'system', 'category': 'configuration'}
                },
                {
                    'content': 'Security level is set to CONFIDENTIAL. The agent maintains military-grade security standards and validates all file paths and commands for security.',
                    'path': 'system://security_info',
                    'metadata': {'type': 'system', 'category': 'security'}
                },
                {
                    'content': 'The agent can help with programming tasks, file operations, web searches, code analysis, system commands, and memory management. It formats code responses using <augment_code_snippet> tags.',
                    'path': 'system://capabilities',
                    'metadata': {'type': 'system', 'category': 'capabilities'}
                }
            ]

            for context in default_contexts:
                self.add_to_context(
                    context['content'],
                    context['path'],
                    context['metadata']
                )

            logger.info(f"Added {len(default_contexts)} default context items")

        except Exception as e:
            logger.warning(f"Error adding default context: {e}")
