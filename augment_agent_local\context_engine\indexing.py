"""
Indexação de Código

Este módulo implementa a indexação de código para o motor de contexto,
permitindo a criação e manutenção de índices de código eficientes.
"""

import os
import re
import json
import pickle
import logging
import hashlib
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Set, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

# Configurar logger
logger = logging.getLogger("augment.indexing")

class CodeChunk:
    """
    Representa um chunk de código com metadados.
    """
    
    def __init__(self, content: str, file_path: str, start_line: int, end_line: int, 
                 language: Optional[str] = None, symbols: Optional[List[str]] = None):
        """
        Inicializa um chunk de código.
        
        Args:
            content: Conteúdo do chunk
            file_path: Caminho do arquivo
            start_line: Linha inicial (1-based)
            end_line: Linha final (1-based)
            language: Linguagem de programação
            symbols: Símbolos importantes no chunk (funções, classes, etc.)
        """
        self.content = content
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.language = language
        self.symbols = symbols or []
        self.embedding = None
        self.metadata = {
            'file_path': file_path,
            'start_line': start_line,
            'end_line': end_line,
            'language': language,
            'symbols': symbols or [],
            'chunk_hash': self._compute_hash()
        }
        
    def _compute_hash(self) -> str:
        """
        Computa um hash para o chunk.
        
        Returns:
            Hash do chunk
        """
        content_hash = hashlib.md5(self.content.encode('utf-8')).hexdigest()
        return f"{self.file_path}:{self.start_line}-{self.end_line}:{content_hash[:8]}"
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Converte o chunk para um dicionário.
        
        Returns:
            Dicionário com os dados do chunk
        """
        return {
            'content': self.content,
            'file_path': self.file_path,
            'start_line': self.start_line,
            'end_line': self.end_line,
            'language': self.language,
            'symbols': self.symbols,
            'metadata': self.metadata
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CodeChunk':
        """
        Cria um chunk a partir de um dicionário.
        
        Args:
            data: Dicionário com os dados do chunk
            
        Returns:
            Chunk de código
        """
        chunk = cls(
            content=data['content'],
            file_path=data['file_path'],
            start_line=data['start_line'],
            end_line=data['end_line'],
            language=data['language'],
            symbols=data['symbols']
        )
        
        # Restaurar metadados adicionais se existirem
        if 'metadata' in data:
            for key, value in data['metadata'].items():
                if key not in chunk.metadata:
                    chunk.metadata[key] = value
                    
        return chunk

class CodeIndex:
    """
    Índice de código que armazena embeddings e metadados para busca eficiente.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o índice de código.
        
        Args:
            config: Configuração do índice
        """
        self.config = config or {}
        self.embedding_dim = self.config.get('embedding_dim', 768)
        self.persist_dir = self.config.get('persist_dir', './index_data')
        self.use_faiss = self.config.get('use_faiss', True)
        self.is_partitioned = self.config.get('is_partitioned', False)
        self.partitions = self.config.get('partitions', [])
        
        # Dados do índice
        self.documents = []  # Lista de CodeChunk
        self.embeddings = np.zeros((0, self.embedding_dim))  # Matriz de embeddings
        self.metadata = []  # Lista de metadados
        self.document_lookup = {}  # Mapeamento de hash para índice
        
        # Índice FAISS para busca eficiente
        self.faiss_index = None
        
        # Inicializar índice FAISS se disponível
        if self.use_faiss:
            self._init_faiss()
            
    def _init_faiss(self):
        """
        Inicializa o índice FAISS.
        """
        try:
            import faiss
            self.faiss_index = faiss.IndexFlatIP(self.embedding_dim)
            logger.info("Índice FAISS inicializado com sucesso")
        except ImportError:
            logger.warning("Biblioteca FAISS não disponível. Usando busca de similaridade padrão.")
            self.use_faiss = False
            
    def add(self, chunk: CodeChunk, embedding: np.ndarray):
        """
        Adiciona um chunk de código ao índice.
        
        Args:
            chunk: Chunk de código
            embedding: Embedding do chunk
        """
        # Verificar se o chunk já existe
        chunk_hash = chunk.metadata['chunk_hash']
        if chunk_hash in self.document_lookup:
            # Atualizar embedding e metadados
            idx = self.document_lookup[chunk_hash]
            self.documents[idx] = chunk
            self.embeddings[idx] = embedding
            self.metadata[idx] = chunk.metadata
            return
            
        # Adicionar novo chunk
        self.documents.append(chunk)
        
        # Adicionar embedding
        if len(self.embeddings) == 0:
            self.embeddings = np.array([embedding])
        else:
            self.embeddings = np.vstack([self.embeddings, embedding])
            
        # Adicionar metadados
        self.metadata.append(chunk.metadata)
        
        # Atualizar lookup
        self.document_lookup[chunk_hash] = len(self.documents) - 1
        
        # Atualizar índice FAISS
        if self.use_faiss and self.faiss_index is not None:
            import faiss
            self.faiss_index.add(np.array([embedding], dtype=np.float32))
            
    def search(self, query_embedding: np.ndarray, top_k: int = 5, 
               filter_criteria: Optional[Dict[str, Any]] = None) -> List[Tuple[int, float, Dict[str, Any]]]:
        """
        Busca chunks de código similares a um embedding de consulta.
        
        Args:
            query_embedding: Embedding da consulta
            top_k: Número máximo de resultados
            filter_criteria: Critérios de filtro para os resultados
            
        Returns:
            Lista de tuplas (índice, similaridade, metadados)
        """
        if self.is_partitioned:
            return self._search_partitioned(query_embedding, top_k, filter_criteria)
            
        if len(self.documents) == 0:
            return []
            
        # Buscar usando FAISS se disponível
        if self.use_faiss and self.faiss_index is not None:
            import faiss
            # Normalizar embedding de consulta
            query_embedding_norm = query_embedding / np.linalg.norm(query_embedding)
            query_embedding_norm = np.array([query_embedding_norm], dtype=np.float32)
            
            # Buscar no índice FAISS
            similarities, indices = self.faiss_index.search(query_embedding_norm, min(top_k * 2, len(self.documents)))
            
            # Converter para o formato de resultado
            results = []
            for i in range(len(indices[0])):
                idx = indices[0][i]
                similarity = similarities[0][i]
                
                # Verificar filtros
                if filter_criteria and not self._matches_filter(self.metadata[idx], filter_criteria):
                    continue
                    
                results.append((idx, float(similarity), self.metadata[idx]))
                
                if len(results) >= top_k:
                    break
                    
            return results
        else:
            # Busca de similaridade padrão
            # Normalizar embedding de consulta
            query_embedding_norm = query_embedding / np.linalg.norm(query_embedding)
            
            # Calcular similaridade com todos os embeddings
            similarities = np.dot(self.embeddings, query_embedding_norm)
            
            # Ordenar por similaridade
            indices = np.argsort(similarities)[::-1]
            
            # Aplicar filtros e retornar resultados
            results = []
            for idx in indices:
                similarity = similarities[idx]
                
                # Verificar filtros
                if filter_criteria and not self._matches_filter(self.metadata[idx], filter_criteria):
                    continue
                    
                results.append((int(idx), float(similarity), self.metadata[idx]))
                
                if len(results) >= top_k:
                    break
                    
            return results
