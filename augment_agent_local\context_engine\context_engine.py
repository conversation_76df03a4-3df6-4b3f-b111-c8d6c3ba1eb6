"""
Motor de Contexto Principal

Este módulo implementa o motor de contexto principal que coordena a indexação,
embedding e recuperação de código para fornecer contexto relevante.
"""

import os
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import threading
from concurrent.futures import ThreadPoolExecutor

from .embedding import EmbeddingModel
from .indexing import CodeIndexer, CodeIndex
from .retrieval import CodeRetriever
from ..utils.cache import MultiLevelCache

# Configurar logger
logger = logging.getLogger("augment.context_engine")

class ContextEngine:
    """
    Motor de contexto avançado que coordena a indexação, embedding e recuperação
    de código para fornecer contexto relevante usando técnicas de machine learning
    e análise de código sofisticadas.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Inicializa o motor de contexto avançado.

        Args:
            config: Configuração opcional para o motor de contexto
        """
        self.config = config or {}

        # Configurações padrão
        self.embedding_dim = self.config.get('embedding_dim', 768)
        self.model_path = self.config.get('model_path', None)
        self.model_type = self.config.get('model_type', 'code')
        self.cache_dir = Path(self.config.get('cache_dir', './context_engine_cache'))
        self.cache_dir.mkdir(exist_ok=True, parents=True)
        self.max_results = self.config.get('max_results', 5)
        self.reindex_interval = self.config.get('reindex_interval', 24 * 60 * 60)  # 24 horas em segundos
        self.max_workers = self.config.get('max_workers', min(32, (os.cpu_count() or 4) + 4))

        # Inicializar componentes
        logger.info("Inicializando motor de contexto avançado")

        # Inicializar cache
        self.cache = MultiLevelCache(
            memory_cache_size=self.config.get('memory_cache_size', 10000),
            memory_ttl=self.config.get('memory_ttl', 3600),
            disk_cache_dir=str(self.cache_dir / 'cache'),
            disk_ttl=self.config.get('disk_ttl', 86400),
            disk_max_size_mb=self.config.get('disk_max_size_mb', 1024)
        )

        # Inicializar modelo de embedding
        self.embedding_model = EmbeddingModel(
            model_path=self.model_path,
            embedding_dim=self.embedding_dim,
            model_type=self.model_type,
            cache=self.cache
        )

        # Configurações para o indexador
        indexer_config = {
            'supported_extensions': self.config.get('supported_extensions', [
                '.py', '.js', '.ts', '.java', '.c', '.cpp', '.cs', '.go', '.rb',
                '.php', '.swift', '.kt', '.rs', '.scala', '.html', '.css', '.jsx', '.tsx'
            ]),
            'max_file_size': self.config.get('max_file_size', 1024 * 1024),  # 1MB
            'max_workers': self.max_workers,
            'chunk_size': self.config.get('chunk_size', 1000),
            'chunk_overlap': self.config.get('chunk_overlap', 200),
            'cache_dir': str(self.cache_dir / 'indexer_cache')
        }

        self.indexer = CodeIndexer(
            embedding_model=self.embedding_model,
            config=indexer_config
        )

        # Configurações para o índice
        index_config = {
            'embedding_dim': self.embedding_dim,
            'persist_dir': str(self.cache_dir / 'index_data'),
            'use_faiss': self.config.get('use_faiss', True)
        }

        # Inicializar o índice
        self.index = CodeIndex(config=index_config)

        # Tentar carregar índice existente
        if self._should_load_index():
            self.index.load()

        # Configurações para o recuperador
        retriever_config = {
            'max_results': self.max_results,
            'min_similarity': self.config.get('min_similarity', 0.5),
            'use_reranking': self.config.get('use_reranking', True)
        }

        self.retriever = CodeRetriever(
            index=self.index,
            config=retriever_config
        )

        # Metadados do motor
        self.metadata = {
            'last_indexed': None,
            'indexed_paths': [],
            'num_documents': 0,
            'partitions': []
        }

        # Lock para operações de indexação
        self.index_lock = threading.RLock()

        # Carregar metadados
        self._load_metadata()

    def _should_load_index(self) -> bool:
        """
        Verifica se deve carregar um índice existente.

        Returns:
            True se deve carregar, False caso contrário
        """
        index_path = self.cache_dir / 'index_data'
        if not index_path.exists():
            return False

        # Verificar se há arquivos de índice
        required_files = ['documents.pkl', 'embeddings.npy', 'metadata.json']
        for file in required_files:
            if not (index_path / file).exists():
                return False

        return True

    def _load_metadata(self):
        """
        Carrega os metadados do motor de contexto.
        """
        metadata_path = self.cache_dir / 'engine_metadata.json'
        if metadata_path.exists():
            try:
                with open(metadata_path, 'r') as f:
                    self.metadata = json.load(f)
                logger.info(f"Metadados carregados: {len(self.metadata['indexed_paths'])} caminhos indexados")
            except Exception as e:
                logger.warning(f"Erro ao carregar metadados: {e}")

    def _save_metadata(self):
        """
        Salva os metadados do motor de contexto.
        """
        metadata_path = self.cache_dir / 'engine_metadata.json'
        try:
            with open(metadata_path, 'w') as f:
                json.dump(self.metadata, f)
        except Exception as e:
            logger.warning(f"Erro ao salvar metadados: {e}")

    def _should_reindex(self, codebase_path: str) -> bool:
        """
        Verifica se deve reindexar o codebase.

        Args:
            codebase_path: Caminho para o codebase

        Returns:
            True se deve reindexar, False caso contrário
        """
        # Se nunca foi indexado, deve indexar
        if self.metadata['last_indexed'] is None:
            return True

        # Se o caminho não está nos caminhos indexados, deve indexar
        if codebase_path not in self.metadata['indexed_paths']:
            return True

        # Se passou do intervalo de reindexação, deve indexar
        last_indexed = self.metadata['last_indexed']
        current_time = time.time()
        if current_time - last_indexed > self.reindex_interval:
            return True

        return False

    def initialize(self, codebase_path: str, force_reindex: bool = False):
        """
        Inicializa o motor de contexto com um determinado codebase.

        Args:
            codebase_path: Caminho para o codebase a ser indexado
            force_reindex: Se deve forçar a reindexação mesmo se já estiver indexado
        """
        logger.info(f"Inicializando motor de contexto com codebase: {codebase_path}")

        with self.index_lock:
            # Verificar se deve reindexar
            if force_reindex or self._should_reindex(codebase_path):
                logger.info("Iniciando indexação do codebase")

                # Indexar o codebase
                start_time = time.time()

                # Verificar tamanho do codebase para decidir se usa particionamento
                codebase_size = self._get_codebase_size(codebase_path)
                use_partitioning = codebase_size > self.config.get('partition_threshold_mb', 100) * 1024 * 1024

                if use_partitioning:
                    logger.info(f"Codebase grande detectado ({codebase_size / (1024*1024):.2f} MB). Usando particionamento.")
                    self._index_with_partitioning(codebase_path)
                else:
                    self.index = self.indexer.index_codebase(codebase_path)

                end_time = time.time()

                # Atualizar metadados
                self.metadata['last_indexed'] = end_time
                if codebase_path not in self.metadata['indexed_paths']:
                    self.metadata['indexed_paths'].append(codebase_path)
                self.metadata['num_documents'] = self.index.size()

                # Salvar metadados
                self._save_metadata()

                # Salvar índice
                self.index.save()

                logger.info(f"Indexação concluída em {end_time - start_time:.2f} segundos. {self.index.size()} documentos indexados.")
            else:
                logger.info(f"Usando índice existente com {self.index.size()} documentos.")

    def _get_codebase_size(self, codebase_path: str) -> int:
        """
        Calcula o tamanho total do codebase em bytes.

        Args:
            codebase_path: Caminho para o codebase

        Returns:
            Tamanho do codebase em bytes
        """
        total_size = 0
        for root, _, files in os.walk(codebase_path):
            for file in files:
                file_path = os.path.join(root, file)
                if self.indexer.should_index_file(file_path):
                    try:
                        total_size += os.path.getsize(file_path)
                    except (OSError, IOError):
                        pass
        return total_size

    def _index_with_partitioning(self, codebase_path: str):
        """
        Indexa um codebase grande usando particionamento.

        Args:
            codebase_path: Caminho para o codebase
        """
        # Limpar partições anteriores
        self.metadata['partitions'] = []

        # Coletar todos os arquivos a serem indexados
        all_files = []
        for root, _, files in os.walk(codebase_path):
            for file in files:
                file_path = os.path.join(root, file)
                if self.indexer.should_index_file(file_path):
                    all_files.append(file_path)

        # Determinar número de partições
        files_per_partition = self.config.get('files_per_partition', 1000)
        num_partitions = max(1, len(all_files) // files_per_partition)

        logger.info(f"Dividindo {len(all_files)} arquivos em {num_partitions} partições")

        # Criar e indexar cada partição
        for i in range(num_partitions):
            start_idx = i * len(all_files) // num_partitions
            end_idx = (i + 1) * len(all_files) // num_partitions

            partition_files = all_files[start_idx:end_idx]
            partition_name = f"partition_{i}"
            partition_dir = self.cache_dir / 'partitions' / partition_name
            partition_dir.mkdir(exist_ok=True, parents=True)

            logger.info(f"Indexando partição {i+1}/{num_partitions} com {len(partition_files)} arquivos")

            # Criar índice para a partição
            partition_index = self.indexer.index_files(partition_files)

            # Salvar índice da partição
            partition_index.save(str(partition_dir))

            # Registrar partição
            self.metadata['partitions'].append({
                'name': partition_name,
                'path': str(partition_dir),
                'num_documents': partition_index.size(),
                'files': len(partition_files)
            })

        # Criar índice principal que referencia as partições
        self.index = CodeIndex(config={
            'embedding_dim': self.embedding_dim,
            'persist_dir': str(self.cache_dir / 'index_data'),
            'use_faiss': self.config.get('use_faiss', True),
            'is_partitioned': True,
            'partitions': self.metadata['partitions']
        })

    def retrieve_context(self, query: str, max_results: Optional[int] = None, filter_criteria: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Recupera contexto relevante com base em uma consulta.

        Args:
            query: Consulta em linguagem natural
            max_results: Número máximo de resultados a retornar
            filter_criteria: Critérios de filtro opcionais para os resultados

        Returns:
            Lista de snippets de código relevantes com metadados
        """
        logger.info(f"Recuperando contexto para consulta: {query[:50]}...")

        # Usar max_results padrão se não fornecido
        if max_results is None:
            max_results = self.max_results

        # Verificar cache
        cache_key = f"query_{hash(query)}_{max_results}"
        cached_results = self.cache.get(cache_key)
        if cached_results is not None:
            logger.info(f"Resultados encontrados no cache para: {query[:50]}...")
            return cached_results

        # Gerar embedding para a consulta
        query_embedding = self.embedding_model.embed_query(query)

        # Recuperar resultados
        start_time = time.time()
        results = self.retriever.retrieve(
            query_embedding,
            max_results=max_results,
            filter_criteria=filter_criteria
        )
        end_time = time.time()

        logger.info(f"Recuperação concluída em {end_time - start_time:.4f} segundos. {len(results)} resultados encontrados.")

        # Armazenar no cache
        self.cache.set(cache_key, results)

        return results

    def analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analisa uma consulta para extrair informações relevantes.

        Args:
            query: Consulta em linguagem natural

        Returns:
            Informações extraídas da consulta
        """
        # Implementação sofisticada com NLP
        import re

        # Detectar tipo de consulta
        query_type = 'code_search'  # padrão

        if any(keyword in query.lower() for keyword in ['como', 'how', 'explain', 'explicar']):
            query_type = 'explanation'

        if any(keyword in query.lower() for keyword in ['erro', 'error', 'bug', 'fix', 'corrigir']):
            query_type = 'error_fix'

        if any(keyword in query.lower() for keyword in ['implementar', 'implement', 'criar', 'create']):
            query_type = 'implementation'

        # Extrair possíveis linguagens de programação
        languages = []
        language_keywords = {
            'python': ['python', 'py', 'django', 'flask'],
            'javascript': ['javascript', 'js', 'node', 'react', 'vue', 'angular'],
            'typescript': ['typescript', 'ts'],
            'java': ['java', 'spring'],
            'c#': ['c#', 'csharp', '.net', 'dotnet'],
            'c++': ['c++', 'cpp'],
            'go': ['go', 'golang'],
            'ruby': ['ruby', 'rails'],
            'php': ['php', 'laravel', 'symfony'],
            'swift': ['swift', 'ios'],
            'kotlin': ['kotlin', 'android']
        }

        for lang, keywords in language_keywords.items():
            if any(keyword in query.lower() for keyword in keywords):
                languages.append(lang)

        # Extrair possíveis arquivos ou caminhos
        file_pattern = r'[\w\-\.\/]+\.\w+'
        files = re.findall(file_pattern, query)

        return {
            'query_type': query_type,
            'languages': languages,
            'files': files,
            'original_query': query
        }

    def get_statistics(self) -> Dict[str, Any]:
        """
        Obtém estatísticas do motor de contexto.

        Returns:
            Estatísticas do motor
        """
        cache_stats = self.cache.get_stats()

        return {
            'num_documents': self.index.size(),
            'indexed_paths': self.metadata['indexed_paths'],
            'last_indexed': self.metadata['last_indexed'],
            'is_partitioned': len(self.metadata['partitions']) > 0,
            'num_partitions': len(self.metadata['partitions']),
            'embedding_model': {
                'type': self.model_type,
                'dimension': self.embedding_dim
            },
            'cache': cache_stats
        }

    def clear_cache(self):
        """
        Limpa o cache do motor de contexto.
        """
        try:
            # Limpar cache
            self.cache.clear()
            logger.info("Cache limpo com sucesso")
        except Exception as e:
            logger.error(f"Erro ao limpar cache: {e}")

    def search_by_content(self, content: str, max_results: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Busca snippets de código pelo conteúdo.

        Args:
            content: Conteúdo a ser buscado
            max_results: Número máximo de resultados a retornar

        Returns:
            Lista de snippets de código relevantes
        """
        # Usar max_results padrão se não fornecido
        if max_results is None:
            max_results = self.max_results

        # Verificar cache
        cache_key = f"content_{hash(content)}_{max_results}"
        cached_results = self.cache.get(cache_key)
        if cached_results is not None:
            return cached_results

        # Gerar embedding para o conteúdo
        content_embedding = self.embedding_model.embed_code(content)

        # Recuperar resultados
        results = self.retriever.retrieve(content_embedding, max_results=max_results)

        # Armazenar no cache
        self.cache.set(cache_key, results)

        return results