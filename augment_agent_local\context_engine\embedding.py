"""
Modelo de Embedding

Este módulo implementa o modelo de embedding que converte código e consultas
em representações vetoriais usando técnicas avançadas de machine learning.
"""

import os
import hashlib
import logging
import numpy as np
from typing import Dict, List, Any, Optional, Union
import time

# Configurar logger
logger = logging.getLogger("augment.embedding")

class EmbeddingModel:
    """
    Modelo de embedding avançado que converte texto em vetores usando
    modelos pré-treinados especializados para código.
    """

    # Modelos utilizados pelo Augment Agent real
    AUGMENT_MODELS = {
        'code_primary': 'microsoft/codebert-base',
        'code_secondary': 'microsoft/graphcodebert-base',
        'text_primary': 'sentence-transformers/all-mpnet-base-v2',
        'text_secondary': 'sentence-transformers/all-MiniLM-L6-v2',
        'multilingual': 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2',
        # Modelos específicos por linguagem
        'python': 'microsoft/graphcodebert-base',
        'javascript': 'microsoft/codebert-base',
        'java': 'microsoft/codebert-base',
        'go': 'microsoft/codebert-base',
        'ruby': 'microsoft/codebert-base',
        'php': 'microsoft/codebert-base',
        'c': 'microsoft/codebert-base',
        'cpp': 'microsoft/codebert-base',
        'csharp': 'microsoft/codebert-base',
    }

    def __init__(self, model_path=None, embedding_dim=768, model_type="code",
                 language=None, cache=None, config=None):
        """
        Inicializa o modelo de embedding.

        Args:
            model_path: Caminho para o modelo pré-treinado
            embedding_dim: Dimensão do embedding (768 para CodeBERT, 384 para MiniLM)
            model_type: Tipo de modelo ('code', 'text', 'multilingual')
            language: Linguagem de programação específica
            cache: Cache para armazenar embeddings
            config: Configurações adicionais
        """
        self.config = config or {}
        self.model_path = model_path
        self.embedding_dim = embedding_dim
        self.model_type = model_type
        self.language = language
        self.cache = cache

        # Modelos primário e secundário
        self.primary_model = None
        self.secondary_model = None
        self.primary_tokenizer = None
        self.secondary_tokenizer = None

        # Estatísticas
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'primary_model_uses': 0,
            'secondary_model_uses': 0,
            'fallback_uses': 0
        }

        # Tentar carregar modelos avançados se disponíveis
        logger.info(f"Inicializando modelo de embedding: tipo={model_type}, linguagem={language}")
        self._load_models()

    def _load_models(self):
        """
        Carrega os modelos de embedding primário e secundário com base nas bibliotecas disponíveis,
        no tipo de modelo solicitado e na linguagem de programação.
        """
        # Verificar se temos bibliotecas de ML disponíveis
        try:
            import torch
            import transformers
            from transformers import AutoModel, AutoTokenizer
            HAVE_TRANSFORMERS = True
            logger.info("Biblioteca Transformers disponível")
        except ImportError:
            HAVE_TRANSFORMERS = False
            logger.warning("Biblioteca Transformers não disponível")

        try:
            import sentence_transformers
            from sentence_transformers import SentenceTransformer
            HAVE_SENTENCE_TRANSFORMERS = True
            logger.info("Biblioteca SentenceTransformers disponível")
        except ImportError:
            HAVE_SENTENCE_TRANSFORMERS = False
            logger.warning("Biblioteca SentenceTransformers não disponível")

        # Determinar quais modelos carregar com base no tipo e linguagem
        primary_model_id = self._get_model_id_for_type_and_language(primary=True)
        secondary_model_id = self._get_model_id_for_type_and_language(primary=False)

        logger.info(f"Tentando carregar modelo primário: {primary_model_id}")
        logger.info(f"Tentando carregar modelo secundário: {secondary_model_id}")

        # Se temos um caminho específico para o modelo, usar ele como primário
        if self.model_path and os.path.exists(self.model_path):
            try:
                if HAVE_SENTENCE_TRANSFORMERS:
                    self.primary_model = SentenceTransformer(self.model_path)
                    logger.info(f"Carregado modelo primário SentenceTransformer de {self.model_path}")
                elif HAVE_TRANSFORMERS:
                    self.primary_tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                    self.primary_model = AutoModel.from_pretrained(self.model_path)
                    logger.info(f"Carregado modelo primário Transformers de {self.model_path}")
            except Exception as e:
                logger.error(f"Erro ao carregar modelo de {self.model_path}: {e}")

        # Se não conseguimos carregar o modelo primário do caminho específico, tentar carregar do ID
        if self.primary_model is None and primary_model_id and HAVE_TRANSFORMERS:
            try:
                # Carregar com Transformers
                self.primary_tokenizer = AutoTokenizer.from_pretrained(primary_model_id)
                self.primary_model = AutoModel.from_pretrained(primary_model_id)
                logger.info(f"Carregado modelo primário Transformers: {primary_model_id}")
            except Exception as e:
                logger.warning(f"Erro ao carregar modelo {primary_model_id}: {e}")

        # Verificar se conseguimos carregar pelo menos um modelo
        if self.primary_model is None:
            logger.warning("Nenhum modelo de embedding pôde ser carregado. Usando simulação.")

    def _get_model_id_for_type_and_language(self, primary=True):
        """
        Determina o ID do modelo a ser carregado com base no tipo e linguagem.

        Args:
            primary: Se é para o modelo primário (True) ou secundário (False)

        Returns:
            ID do modelo a ser carregado
        """
        # Se temos uma linguagem específica e ela está nos modelos suportados
        if self.language and self.language.lower() in self.AUGMENT_MODELS:
            if primary:
                return self.AUGMENT_MODELS[self.language.lower()]
            else:
                # Para o modelo secundário, usar o modelo de código genérico
                return self.AUGMENT_MODELS['code_secondary']

        # Caso contrário, usar o modelo baseado no tipo
        if self.model_type == 'code':
            return self.AUGMENT_MODELS['code_primary'] if primary else self.AUGMENT_MODELS['code_secondary']
        elif self.model_type == 'text':
            return self.AUGMENT_MODELS['text_primary'] if primary else self.AUGMENT_MODELS['text_secondary']
        elif self.model_type == 'multilingual':
            return self.AUGMENT_MODELS['multilingual']
        else:
            # Tipo desconhecido, usar código como padrão
            return self.AUGMENT_MODELS['code_primary'] if primary else self.AUGMENT_MODELS['code_secondary']

    def embed_query(self, query):
        """
        Converte uma consulta em linguagem natural em um embedding.

        Args:
            query: Consulta em linguagem natural

        Returns:
            Vetor de embedding
        """
        # Verificar cache primeiro
        if self.cache:
            cache_key = f"query_embed_{self._get_cache_key(query)}"
            cached_embedding = self.cache.get(cache_key)
            if cached_embedding is not None:
                self.stats['cache_hits'] += 1
                return cached_embedding
            self.stats['cache_misses'] += 1

        # Pré-processamento da consulta
        processed_query = self._preprocess_query(query)

        # Geração de embedding
        embedding = None

        # Tentar gerar com o modelo primário
        if self.primary_model is not None:
            try:
                import torch

                # Verificar tipo de modelo
                if 'SentenceTransformer' in str(type(self.primary_model)):
                    # Usar SentenceTransformers
                    embedding = self.primary_model.encode(
                        processed_query,
                        convert_to_numpy=True,
                        normalize_embeddings=True
                    )
                    self.stats['primary_model_uses'] += 1
                elif hasattr(self.primary_model, 'forward') and self.primary_tokenizer:
                    # Usar Transformers
                    inputs = self.primary_tokenizer(
                        processed_query,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=512
                    )

                    with torch.no_grad():
                        outputs = self.primary_model(**inputs)

                    # Usar a representação [CLS] para consultas
                    embedding = outputs.last_hidden_state[:, 0, :].numpy().flatten()

                    # Normalizar
                    embedding = embedding / np.linalg.norm(embedding)
                    self.stats['primary_model_uses'] += 1
            except Exception as e:
                logger.warning(f"Erro ao gerar embedding para consulta: {e}")

        # Se o modelo falhou, usar embedding de fallback
        if embedding is None:
            logger.warning(f"Usando sistema de fallback para consulta: {query[:30]}...")
            embedding = self._simulate_embedding(processed_query, is_code=False)
            self.stats['fallback_uses'] += 1

        # Garantir dimensão correta
        if embedding.shape[0] != self.embedding_dim:
            if embedding.shape[0] > self.embedding_dim:
                embedding = embedding[:self.embedding_dim]
            else:
                padding = np.zeros(self.embedding_dim - embedding.shape[0])
                embedding = np.concatenate([embedding, padding])

            # Renormalizar após ajuste de dimensão
            embedding = embedding / np.linalg.norm(embedding)

        # Salvar no cache
        if self.cache:
            self.cache.set(cache_key, embedding)

        return embedding

    def embed_code(self, code):
        """
        Converte um snippet de código em um embedding.

        Args:
            code: Snippet de código

        Returns:
            Vetor de embedding
        """
        # Verificar cache primeiro
        if self.cache:
            cache_key = f"code_embed_{self._get_cache_key(code)}"
            cached_embedding = self.cache.get(cache_key)
            if cached_embedding is not None:
                self.stats['cache_hits'] += 1
                return cached_embedding
            self.stats['cache_misses'] += 1

        # Detectar linguagem
        language = self._detect_language(code)

        # Pré-processamento específico para a linguagem
        processed_code = self._preprocess_code(code, language)

        # Geração de embedding
        embedding = None

        # Tentar gerar com o modelo primário
        if self.primary_model is not None:
            try:
                import torch

                # Verificar tipo de modelo
                if 'SentenceTransformer' in str(type(self.primary_model)):
                    # Usar SentenceTransformers
                    embedding = self.primary_model.encode(
                        processed_code,
                        convert_to_numpy=True,
                        normalize_embeddings=True
                    )
                    self.stats['primary_model_uses'] += 1
                elif hasattr(self.primary_model, 'forward') and self.primary_tokenizer:
                    # Usar Transformers
                    inputs = self.primary_tokenizer(
                        processed_code,
                        return_tensors="pt",
                        padding=True,
                        truncation=True,
                        max_length=512
                    )

                    with torch.no_grad():
                        outputs = self.primary_model(**inputs)

                    # Usar média dos tokens para código
                    embedding = outputs.last_hidden_state.mean(dim=1).numpy().flatten()

                    # Normalizar
                    embedding = embedding / np.linalg.norm(embedding)
                    self.stats['primary_model_uses'] += 1
            except Exception as e:
                logger.warning(f"Erro ao gerar embedding para código: {e}")

        # Se o modelo falhou, usar embedding de fallback
        if embedding is None:
            logger.warning(f"Usando sistema de fallback para código")
            embedding = self._simulate_embedding(processed_code, is_code=True, language=language)
            self.stats['fallback_uses'] += 1

        # Garantir dimensão correta
        if embedding.shape[0] != self.embedding_dim:
            if embedding.shape[0] > self.embedding_dim:
                embedding = embedding[:self.embedding_dim]
            else:
                padding = np.zeros(self.embedding_dim - embedding.shape[0])
                embedding = np.concatenate([embedding, padding])

            # Renormalizar após ajuste de dimensão
            embedding = embedding / np.linalg.norm(embedding)

        # Salvar no cache
        if self.cache:
            self.cache.set(cache_key, embedding)

        return embedding

    def _get_cache_key(self, text):
        """
        Gera uma chave de cache para um texto.

        Args:
            text: Texto para gerar a chave

        Returns:
            Chave de cache
        """
        # Normalizar texto para consistência
        normalized_text = text.strip()

        # Gerar hash do texto
        text_hash = hashlib.md5(normalized_text.encode('utf-8')).hexdigest()

        # Formato: modeltype_hash
        return f"{self.model_type}_{text_hash}"

    def _simulate_embedding(self, text, is_code=False, language=None):
        """
        Gera um embedding de fallback quando os modelos principais falham.

        Args:
            text: Texto para gerar embedding
            is_code: Se o texto é código
            language: Linguagem de programação (se for código)

        Returns:
            Embedding de fallback
        """
        # Dividir o texto em tokens
        tokens = text.split()

        # Inicializar embedding com zeros
        embedding = np.zeros(self.embedding_dim)

        # Para cada token, adicionar sua contribuição ao embedding
        for i, token in enumerate(tokens):
            # Usar hash do token para gerar valores consistentes
            token_hash = int(hashlib.md5(token.encode('utf-8')).hexdigest(), 16)
            np.random.seed(token_hash)

            # Gerar vetor para o token
            token_vec = np.random.randn(self.embedding_dim)

            # Adicionar ao embedding com peso baseado na posição
            # Para código, dar mais peso a tokens importantes como nomes de funções e classes
            if is_code:
                # Identificar tokens importantes em código
                important_tokens = ['def', 'class', 'function', 'import', 'from', 'public', 'private']
                if language == 'python':
                    important_tokens.extend(['self', 'return', 'if', 'for', 'while'])
                elif language in ['javascript', 'typescript']:
                    important_tokens.extend(['const', 'let', 'var', 'return', 'this'])

                # Dar mais peso a tokens importantes
                if token in important_tokens:
                    weight = 2.0 / (1 + i * 0.05)
                else:
                    weight = 1.0 / (1 + i * 0.1)
            else:
                # Para consultas, dar peso decrescente com a posição
                weight = 1.0 / (1 + i * 0.1)

            embedding += token_vec * weight

        # Se não houver tokens, gerar um vetor aleatório
        if not tokens:
            np.random.seed(hash(text) % 2**32)
            embedding = np.random.randn(self.embedding_dim)

        # Normalizar o vetor
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm

        return embedding

    def _preprocess_query(self, query):
        """
        Pré-processa uma consulta em linguagem natural.

        Args:
            query: Consulta original

        Returns:
            Consulta pré-processada
        """
        # Normalização básica
        return query.strip()

    def _preprocess_code(self, code, language=None):
        """
        Pré-processa um snippet de código.

        Args:
            code: Código original
            language: Linguagem de programação detectada

        Returns:
            Código pré-processado
        """
        # Se a linguagem não foi fornecida, tentar detectá-la
        if not language:
            language = self._detect_language(code)

        # Normalização básica
        lines = code.split('\n')
        processed_lines = []

        # Processamento específico por linguagem
        if language == 'python':
            # Processamento específico para Python
            for line in lines:
                # Remover comentários muito longos
                if '#' in line:
                    comment_pos = line.find('#')
                    if len(line[comment_pos:]) > 50:
                        line = line[:comment_pos] + '# ...'

                # Preservar linha se não estiver vazia após processamento
                if line.strip():
                    processed_lines.append(line)
        else:
            # Processamento genérico para outras linguagens
            for line in lines:
                # Preservar linha se não estiver vazia
                if line.strip():
                    processed_lines.append(line)

        # Juntar linhas processadas
        processed_code = '\n'.join(processed_lines)

        # Normalização adicional
        # Remover espaços em branco extras
        processed_code = '\n'.join(line.rstrip() for line in processed_code.split('\n'))

        # Remover linhas em branco consecutivas
        while '\n\n\n' in processed_code:
            processed_code = processed_code.replace('\n\n\n', '\n\n')

        return processed_code

    def _detect_language(self, code):
        """
        Detecta a linguagem de programação de um snippet de código.

        Args:
            code: Snippet de código

        Returns:
            Linguagem detectada ou None se não for possível detectar
        """
        # Implementação simplificada
        if "def " in code and "import " in code:
            return "python"
        elif "function " in code or "const " in code or "let " in code:
            return "javascript"
        elif "public class " in code or "private class " in code:
            return "java"
        elif "#include" in code:
            return "cpp"
        elif "using namespace" in code or "using System" in code:
            return "csharp"

        return None

    def get_model_info(self):
        """
        Obtém informações sobre o modelo de embedding.

        Returns:
            Informações sobre o modelo
        """
        # Informações básicas
        info = {
            'name': 'Augment Embedding Model',
            'version': '1.0.0',
            'type': self.model_type,
            'language': self.language,
            'embedding_dim': self.embedding_dim
        }

        # Adicionar estatísticas
        info['stats'] = self.stats.copy()

        # Adicionar informações sobre modelos carregados
        info['primary_model'] = {
            'type': type(self.primary_model).__name__ if self.primary_model else None,
            'available': self.primary_model is not None
        }

        return info